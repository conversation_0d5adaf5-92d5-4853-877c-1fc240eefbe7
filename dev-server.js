// Simple development server with live reload
const express = require('express');
const path = require('path');
const chokidar = require('chokidar');
const WebSocket = require('ws');
const http = require('http');

const app = express();
const server = http.createServer(app);
const wss = new WebSocket.Server({ server });

// Serve static files
app.use(express.static('.'));

// Inject live reload script into HTML files
app.get('*.html', (req, res) => {
    const filePath = path.join(__dirname, req.path);
    const fs = require('fs');
    
    if (fs.existsSync(filePath)) {
        let html = fs.readFileSync(filePath, 'utf8');
        
        // Inject live reload script before closing body tag
        const liveReloadScript = `
        <script>
            (function() {
                const ws = new WebSocket('ws://localhost:3000');
                ws.onmessage = function(event) {
                    if (event.data === 'reload') {
                        window.location.reload();
                    }
                };
                ws.onclose = function() {
                    // Try to reconnect every 2 seconds
                    setTimeout(() => window.location.reload(), 2000);
                };
            })();
        </script>
        `;
        
        html = html.replace('</body>', liveReloadScript + '</body>');
        res.send(html);
    } else {
        res.status(404).send('File not found');
    }
});

// Watch for file changes
const watcher = chokidar.watch([
    'public/css/**/*.css',
    'public/js/**/*.js',
    '*.html'
], {
    ignored: /node_modules/,
    persistent: true
});

watcher.on('change', (filePath) => {
    console.log(`File changed: ${filePath}`);
    
    // Broadcast reload message to all connected clients
    wss.clients.forEach(client => {
        if (client.readyState === WebSocket.OPEN) {
            client.send('reload');
        }
    });
});

const PORT = 3000;
server.listen(PORT, () => {
    console.log(`🚀 Development server running at http://localhost:${PORT}`);
    console.log(`📁 Serving files from: ${__dirname}`);
    console.log(`👀 Watching for changes in CSS, JS, and HTML files`);
    console.log(`🔄 Auto-reload enabled - changes will refresh the browser automatically`);
});
