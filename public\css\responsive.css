/* Naroop Responsive Design */

/* Mobile First Approach */

/* Extra Small Devices (phones, 576px and down) */
@media (max-width: 575.98px) {
    .container {
        padding: 0 var(--spacing-sm);
    }

    .main-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
        padding: var(--spacing-sm);
    }

    .header-content {
        padding: 0 var(--spacing-sm);
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }

    .search-container {
        order: 3;
        width: 100%;
        margin: 0;
    }

    .user-actions {
        gap: var(--spacing-xs);
    }

    .logo {
        font-size: var(--font-size-xl);
    }

    /* Hide sidebar and trending on mobile */
    .sidebar,
    .trending {
        display: none;
    }

    /* Adjust content sections for mobile */
    .content-section {
        margin-bottom: var(--spacing-md);
        border-radius: var(--border-radius-md);
    }

    .feed-header {
        padding: var(--spacing-md);
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-sm);
    }

    .feed-content {
        padding: var(--spacing-md);
    }

    .story-prompt {
        padding: var(--spacing-md);
        margin-bottom: var(--spacing-md);
    }

    /* Adjust button sizes */
    .btn-base {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
    }

    .btn-lg {
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--font-size-base);
    }

    /* Profile section mobile adjustments */
    .profile-header {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }

    .profile-stats {
        flex-direction: row;
        justify-content: space-around;
    }

    /* Empty state mobile adjustments */
    .empty-state {
        padding: var(--spacing-lg);
    }

    .empty-state-icon {
        font-size: 3rem;
    }
    
    /* Modal adjustments */
    .auth-modal-content {
        width: 95%;
        margin: var(--spacing-sm);
    }
    
    /* Form adjustments */
    .form-container {
        padding: var(--spacing-md);
    }
    
    .form-buttons {
        flex-direction: column;
    }
    
    /* Typography adjustments */
    h1 { font-size: var(--font-size-3xl); }
    h2 { font-size: var(--font-size-2xl); }
    h3 { font-size: var(--font-size-xl); }
}

/* Small Devices (landscape phones, 576px and up) */
@media (min-width: 576px) and (max-width: 767.98px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
        padding: var(--spacing-md) 0;
    }

    .sidebar,
    .trending {
        display: none;
    }

    .search-container {
        max-width: 400px;
    }

    .feed-header {
        padding: var(--spacing-lg);
    }

    .feed-content {
        padding: var(--spacing-lg);
    }
}

/* Medium Devices (tablets, 768px and up) */
@media (min-width: 768px) and (max-width: 991.98px) {
    .main-content {
        grid-template-columns: 250px 1fr;
        gap: var(--spacing-md);
        padding: var(--spacing-md) 0;
    }

    .sidebar {
        width: 250px;
    }

    .trending {
        display: none;
    }

    .search-container {
        max-width: 350px;
    }
    
    /* Adjust navigation */
    .nav-item {
        padding: var(--spacing-sm);
    }
    
    .nav-item span {
        font-size: var(--font-size-sm);
    }
}

/* Large Devices (desktops, 992px and up) */
@media (min-width: 992px) and (max-width: 1199.98px) {
    .main-content {
        grid-template-columns: var(--sidebar-width) 1fr 280px;
    }
    
    .search-container {
        max-width: 450px;
    }
}

/* Extra Large Devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
    .container {
        max-width: 1200px;
    }
    
    .main-content {
        grid-template-columns: var(--sidebar-width) 1fr 320px;
    }
    
    .search-container {
        max-width: 500px;
    }
}

/* Ultra Wide Screens (1400px and up) */
@media (min-width: 1400px) {
    .container {
        max-width: 1400px;
    }
    
    .main-content {
        grid-template-columns: 320px 1fr 360px;
        gap: var(--spacing-xl);
    }
}

/* Landscape Orientation */
@media (orientation: landscape) and (max-height: 600px) {
    .header {
        height: 50px;
    }
    
    .main-content {
        padding: var(--spacing-sm) 0;
    }
    
    .auth-modal-content {
        max-height: 80vh;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    /* Optimize for retina displays */
    .logo {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
    
    .btn-base {
        -webkit-font-smoothing: antialiased;
    }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
    /* Increase touch targets */
    .btn-base {
        min-height: 44px;
        min-width: 44px;
    }
    
    .nav-item {
        min-height: 48px;
    }
    
    .profile-img {
        width: 44px;
        height: 44px;
    }
    
    .preferences-btn,
    .notification-btn {
        min-width: 44px;
        min-height: 44px;
    }
    
    /* Remove hover effects */
    .hover-lift:hover,
    .hover-grow:hover,
    .hover-float:hover {
        transform: none;
    }
    
    /* Adjust form inputs for touch */
    .form-input,
    input,
    textarea,
    select {
        min-height: 44px;
        font-size: 16px; /* Prevent zoom on iOS */
    }
}

/* Print Styles */
@media print {
    .header,
    .sidebar,
    .user-actions,
    .btn-base,
    .modal-overlay {
        display: none !important;
    }
    
    .main-content {
        grid-template-columns: 1fr;
        gap: 0;
    }
    
    .card-base {
        border: 1px solid #000;
        box-shadow: none;
        page-break-inside: avoid;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
    
    a {
        color: black !important;
        text-decoration: underline !important;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .main-content,
    .card-base,
    .btn-base,
    .nav-item,
    .search-input,
    .profile-img {
        transition: none !important;
        animation: none !important;
    }
    
    .hover-lift:hover,
    .hover-grow:hover,
    .hover-float:hover {
        transform: none !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .card-base {
        border-width: 2px;
    }
    
    .btn-base {
        border: 2px solid currentColor;
    }
    
    .form-input,
    input,
    textarea,
    select {
        border-width: 2px;
    }
}

/* Dark Mode Adjustments */
@media (prefers-color-scheme: dark) {
    /* Additional dark mode specific responsive adjustments */
    .search-input::placeholder {
        color: var(--text-muted);
    }
    
    .form-input::placeholder,
    input::placeholder,
    textarea::placeholder {
        color: var(--text-muted);
    }
}

/* Container Queries (Future Enhancement) */
/* When container queries are widely supported */
/*
@container (max-width: 400px) {
    .card-base {
        padding: var(--spacing-sm);
    }
}
*/

/* Utility Classes for Responsive Design */
.hide-mobile {
    display: block;
}

.show-mobile {
    display: none;
}

@media (max-width: 767.98px) {
    .hide-mobile {
        display: none !important;
    }
    
    .show-mobile {
        display: block !important;
    }
    
    .show-mobile-flex {
        display: flex !important;
    }
    
    .show-mobile-grid {
        display: grid !important;
    }
}

.hide-tablet {
    display: block;
}

.show-tablet {
    display: none;
}

@media (min-width: 768px) and (max-width: 1023.98px) {
    .hide-tablet {
        display: none !important;
    }
    
    .show-tablet {
        display: block !important;
    }
}

.hide-desktop {
    display: block;
}

.show-desktop {
    display: none;
}

@media (min-width: 1024px) {
    .hide-desktop {
        display: none !important;
    }
    
    .show-desktop {
        display: block !important;
    }
}
