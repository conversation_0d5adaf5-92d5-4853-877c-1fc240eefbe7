/* Naroop Main App (index.html) Specific Styles */

/* Main content layout - consolidated and cleaned up */
.main-content {
    display: grid;
    grid-template-columns: var(--sidebar-width) 1fr 300px;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg) 0;
    max-width: var(--container-max-width);
    margin: 0 auto;
    min-height: calc(100vh - var(--header-height));
}

/* Sidebar styles */
.sidebar {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    height: fit-content;
    position: sticky;
    top: calc(var(--header-height) + var(--spacing-lg));
}

.nav-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    margin-bottom: var(--spacing-xs);
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
    position: relative;
}

.nav-item:hover {
    background: var(--surface-color);
    color: var(--text-primary);
    transform: translateX(4px);
}

.nav-item.active {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-md);
}

.nav-item.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 60%;
    background: white;
    border-radius: 0 2px 2px 0;
}

.nav-icon {
    font-size: var(--font-size-lg);
    width: 24px;
    text-align: center;
}

.nav-item-icon {
    font-size: var(--font-size-lg);
    width: 24px;
    text-align: center;
}

.nav-item-text {
    font-weight: var(--font-weight-medium);
}

/* Content sections */
.content-section {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 0;
    margin-bottom: var(--spacing-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: box-shadow var(--transition-fast);
}

.content-section:hover {
    box-shadow: var(--shadow-md);
}

.feed-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    background: var(--surface-color);
    border-bottom: 1px solid var(--divider-color);
}

.feed-content {
    padding: var(--spacing-lg);
}

/* Story prompt styling */
.story-prompt {
    background: var(--gradient-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    text-align: center;
}

.story-prompt h4 {
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
    font-size: var(--font-size-lg);
}

.story-prompt p {
    margin-bottom: var(--spacing-lg);
    color: var(--text-secondary);
}

.feed-title {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-semibold);
    margin: 0;
}

.create-post-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-fast);
}

.create-post-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Trending section */
.trending-section {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    height: fit-content;
    position: sticky;
    top: calc(var(--header-height) + var(--spacing-lg));
}

.trending-header {
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--divider-color);
}

.trending-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    margin: 0;
}

.trending-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--divider-color);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.trending-item:last-child {
    border-bottom: none;
}

.trending-item:hover {
    background: var(--surface-color);
    margin: 0 calc(-1 * var(--spacing-sm));
    padding-left: var(--spacing-sm);
    padding-right: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
}

.trending-rank {
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
    min-width: 20px;
}

.trending-topic {
    flex: 1;
    font-weight: var(--font-weight-medium);
}

.trending-count {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

/* Trending sidebar */
.trending {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    height: fit-content;
    position: sticky;
    top: calc(var(--header-height) + var(--spacing-lg));
}

.trending h3 {
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
}

/* Profile section styles */
.profile-content {
    padding: var(--spacing-lg);
}

.profile-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--divider-color);
}

.profile-avatar {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-2xl);
    color: white;
}

.profile-info h3 {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--text-primary);
    font-size: var(--font-size-lg);
}

.profile-info p {
    margin: 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.profile-stats {
    display: flex;
    justify-content: space-around;
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md) 0;
    background: var(--surface-color);
    border-radius: var(--border-radius-md);
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.profile-actions {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: center;
}

/* Empty states */
.empty-state {
    text-align: center;
    padding: var(--spacing-xxl);
    color: var(--text-muted);
    background: var(--gradient-card);
    border-radius: var(--border-radius-lg);
    border: 1px dashed var(--border-color);
    margin: var(--spacing-lg) 0;
}

.empty-state-icon {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.6;
}

.empty-state h3, .empty-state h4 {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
    font-weight: var(--font-weight-semibold);
}

.empty-state p {
    margin-bottom: var(--spacing-lg);
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
    line-height: var(--line-height-relaxed);
}

/* Error states */
.error-state {
    text-align: center;
    padding: var(--spacing-xxl);
    color: var(--error-color);
}

.error-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-lg);
}

/* Responsive design moved to responsive.css to avoid conflicts */